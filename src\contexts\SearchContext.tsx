import React, { createContext, useContext, useState, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { SearchItem, SearchAction, searchData } from "@/config/searchConfig";

interface SearchContextType {
  executeSearch: (item: SearchItem) => Promise<void>;
  triggerAction: (action: SearchAction) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

const SearchContext = createContext<SearchContextType | undefined>(undefined);

export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error("useSearch must be used within a SearchProvider");
  }
  return context;
};

interface SearchProviderProps {
  children: React.ReactNode;
}

export const SearchProvider: React.FC<SearchProviderProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Execute search result (either navigate to page or trigger action)
  const executeSearch = useCallback(async (item: SearchItem) => {
    setIsLoading(true);
    setError(null);

    try {
      if (item.url) {
        // Navigate to page
        navigate(item.url);
        toast({
          title: "Navigation",
          description: `Navigated to ${item.title}`,
        });
      } else if (item.action) {
        // Trigger action
        await triggerAction(item.action);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Search execution failed";
      setError(errorMessage);
      toast({
        title: "Search Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [navigate, toast]);

  // Trigger specific actions
  const triggerAction = useCallback(async (action: SearchAction) => {
    setIsLoading(true);
    setError(null);

    try {
      // If action requires navigation to a specific page
      if (action.page && location.pathname !== action.page) {
        navigate(action.page);
        // Wait for navigation to complete
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Trigger modal or action
      if (action.modal) {
        await triggerModal(action.modal, action.page);
      } else if (action.action) {
        await triggerPageAction(action.action);
      }

      toast({
        title: "Action Executed",
        description: `Successfully executed ${action.modal || action.action}`,
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Action execution failed";
      setError(errorMessage);
      toast({
        title: "Action Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [navigate, location.pathname, toast]);

  // Trigger modal based on modal name and current page
  const triggerModal = useCallback(async (modalName: string, targetPage?: string) => {
    // Wait a bit for page to load if we just navigated
    if (targetPage && location.pathname !== targetPage) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // Dispatch custom events to trigger modals
    const modalEvent = new CustomEvent('openModal', {
      detail: { modalName, page: targetPage || location.pathname }
    });
    window.dispatchEvent(modalEvent);

    // Also try to trigger modal through global state if available
    switch (modalName) {
      case 'addProduct':
        triggerGlobalAction('openProductModal');
        break;
      case 'createCoupon':
        triggerGlobalAction('openCouponModal');
        break;
      case 'createDiscount':
        triggerGlobalAction('openDiscountModal');
        break;
      case 'addInventory':
        triggerGlobalAction('openInventoryModal');
        break;
      case 'addPurchase':
        triggerGlobalAction('openPurchaseModal');
        break;
      case 'addStaff':
        triggerGlobalAction('openStaffModal');
        break;
      default:
        console.warn(`Unknown modal: ${modalName}`);
    }
  }, [location.pathname]);

  // Trigger page-specific actions
  const triggerPageAction = useCallback(async (actionName: string) => {
    const actionEvent = new CustomEvent('triggerAction', {
      detail: { actionName, page: location.pathname }
    });
    window.dispatchEvent(actionEvent);

    switch (actionName) {
      case 'exportCSV':
        triggerGlobalAction('exportCSV');
        break;
      case 'refresh':
        triggerGlobalAction('refreshData');
        break;
      case 'sendMessage':
        triggerGlobalAction('openMessageModal');
        break;
      default:
        console.warn(`Unknown action: ${actionName}`);
    }
  }, [location.pathname]);

  // Helper function to trigger global actions
  const triggerGlobalAction = useCallback((action: string) => {
    const globalEvent = new CustomEvent('globalAction', {
      detail: { action, page: location.pathname }
    });
    window.dispatchEvent(globalEvent);
  }, [location.pathname]);

  const value: SearchContextType = {
    executeSearch,
    triggerAction,
    isLoading,
    error,
  };

  return (
    <SearchContext.Provider value={value}>
      {children}
    </SearchContext.Provider>
  );
};

// Hook for components to listen to search-triggered events
export const useSearchEvents = () => {
  const [modalToOpen, setModalToOpen] = useState<string | null>(null);
  const [actionToTrigger, setActionToTrigger] = useState<string | null>(null);

  React.useEffect(() => {
    const handleModalEvent = (event: CustomEvent) => {
      setModalToOpen(event.detail.modalName);
    };

    const handleActionEvent = (event: CustomEvent) => {
      setActionToTrigger(event.detail.actionName);
    };

    const handleGlobalAction = (event: CustomEvent) => {
      setActionToTrigger(event.detail.action);
    };

    window.addEventListener('openModal', handleModalEvent as EventListener);
    window.addEventListener('triggerAction', handleActionEvent as EventListener);
    window.addEventListener('globalAction', handleGlobalAction as EventListener);

    return () => {
      window.removeEventListener('openModal', handleModalEvent as EventListener);
      window.removeEventListener('triggerAction', handleActionEvent as EventListener);
      window.removeEventListener('globalAction', handleGlobalAction as EventListener);
    };
  }, []);

  const clearModal = useCallback(() => setModalToOpen(null), []);
  const clearAction = useCallback(() => setActionToTrigger(null), []);

  return {
    modalToOpen,
    actionToTrigger,
    clearModal,
    clearAction,
  };
};
