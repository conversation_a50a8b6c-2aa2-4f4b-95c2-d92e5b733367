import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ShoppingBag, Calendar, User, CreditCard, Package } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface OrderItem {
  name: string;
  sku: string;
  price: number;
  quantity: number;
}

interface Customer {
  name: string;
  email: string;
}

interface Order {
  orderNumber: string;
  customer: Customer;
  date: string;
  items: OrderItem[];
  amount: string;
  status: string;
  subtotal: number;
  shipping: number;
  tax: number;
}

interface OrderViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: Order | null;
}

const OrderViewModal: React.FC<OrderViewModalProps> = ({
  isOpen,
  onClose,
  order,
}) => {
  const [orderStatus, setOrderStatus] = useState(order?.status || "");
  const { toast } = useToast();

  if (!order) return null;

  const handleStatusUpdate = (newStatus: string) => {
    setOrderStatus(newStatus);
    toast({
      title: "Status Updated",
      description: `Order status changed to ${newStatus}`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processing":
        return "bg-blue-100 text-blue-700";
      case "shipped":
        return "bg-purple-100 text-purple-700";
      case "delivered":
        return "bg-green-100 text-green-700";
      case "cancelled":
        return "bg-red-100 text-red-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <ShoppingBag className="w-6 h-6" />
            Order Details - {order.orderNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium text-lg text-gray-900 dark:text-white">
                    {order.customer.name}
                  </p>
                  <p className="text-base text-gray-500 dark:text-gray-400">
                    {order.customer.email}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium text-lg text-gray-900 dark:text-white">
                    Order Date
                  </p>
                  <p className="text-base text-gray-500 dark:text-gray-400">
                    {order.date}
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Package className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium text-lg text-gray-900 dark:text-white">
                    Status
                  </p>
                  <Badge className={`${getStatusColor(orderStatus)} text-base`}>
                    {orderStatus}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <CreditCard className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium text-lg text-gray-900 dark:text-white">
                    Total Amount
                  </p>
                  <p className="text-xl font-bold text-gray-900 dark:text-white">
                    {order.amount}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Order Items */}
          <div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Order Items
            </h3>
            <div className="space-y-3">
              {order.items.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <div className="flex-1">
                    <h4 className="font-medium text-lg text-gray-900 dark:text-white">
                      {item.name}
                    </h4>
                    <p className="text-base text-gray-500 dark:text-gray-400">
                      SKU: {item.sku}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-lg text-gray-900 dark:text-white">
                      ₹{item.price} × {item.quantity}
                    </p>
                    <p className="text-base font-semibold text-gray-900 dark:text-white">
                      ₹{item.price * item.quantity}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Order Summary */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Order Summary
            </h3>
            <div className="space-y-2">
              <div className="flex justify-between text-lg">
                <span className="text-gray-600 dark:text-gray-300">
                  Subtotal:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ₹{order.subtotal}
                </span>
              </div>
              <div className="flex justify-between text-lg">
                <span className="text-gray-600 dark:text-gray-300">
                  Shipping:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ₹{order.shipping}
                </span>
              </div>
              <div className="flex justify-between text-lg">
                <span className="text-gray-600 dark:text-gray-300">Tax:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ₹{order.tax}
                </span>
              </div>
              <Separator />
              <div className="flex justify-between text-xl font-bold">
                <span className="text-gray-900 dark:text-white">Total:</span>
                <span className="text-gray-900 dark:text-white">
                  {order.amount}
                </span>
              </div>
            </div>
          </div>

          {/* Status Update */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              Update Status
            </h3>
            <div className="flex items-center gap-4">
              <Select value={orderStatus} onValueChange={handleStatusUpdate}>
                <SelectTrigger className="w-full max-w-xs text-lg">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center pt-4">
          <Button
            className="bg-green-600 hover:bg-green-700 text-white text-lg px-6 py-3"
            onClick={() => {
              // TODO: Integrate with Shiprocket API for tracking functionality
              toast({
                title: "Shiprocket Integration",
                description:
                  "Tracking feature will be connected to Shiprocket API",
              });
            }}
          >
            Track with Shiprocket
          </Button>
          <Button
            variant="outline"
            onClick={onClose}
            className="text-lg px-6 py-3"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OrderViewModal;
