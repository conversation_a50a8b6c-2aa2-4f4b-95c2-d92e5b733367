import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface Coupon {
  id: number;
  code: string;
  description: string;
  type: string;
  value: number;
  minOrder: number;
  maxDiscount: number;
  usageLimit: number;
  used: number;
  status: string;
  validFrom: string;
  validTo: string;
  productName: string;
  category: string;
  controlType?: string;
}

interface CouponViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  coupon: Coupon | null;
}

const CouponViewModal: React.FC<CouponViewModalProps> = ({
  isOpen,
  onClose,
  coupon,
}) => {
  if (!coupon) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "Inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
    }
  };

  const formatCurrency = (amount: number) => {
    return `₹${amount.toLocaleString()}`;
  };

  const formatPercentage = (value: number) => {
    return `${value}%`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <DialogHeader className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            Coupon Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 p-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Coupon Code
                </label>
                <div className="text-xl font-bold text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border">
                  {coupon.code}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <p className="text-base text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border">
                  {coupon.description}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Control Type
                </label>
                <p className="text-base text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border">
                  {coupon.controlType || "Single Control"}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <Badge className={`${getStatusColor(coupon.status)} text-base px-3 py-1`}>
                  {coupon.status}
                </Badge>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Discount Type & Value
                </label>
                <div className="text-lg font-semibold text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border">
                  {coupon.type === "Percentage" 
                    ? formatPercentage(coupon.value)
                    : formatCurrency(coupon.value)
                  } ({coupon.type})
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Product Name
                </label>
                <p className="text-base text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border">
                  {coupon.productName}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Category
                </label>
                <Badge variant="outline" className="text-base px-3 py-1">
                  {coupon.category}
                </Badge>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Valid Period
                </label>
                <p className="text-base text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg border">
                  {coupon.validFrom} to {coupon.validTo}
                </p>
              </div>
            </div>
          </div>

          {/* Usage & Limits */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Usage & Limits
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <label className="block text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                  Minimum Order
                </label>
                <p className="text-xl font-bold text-blue-900 dark:text-blue-100">
                  {formatCurrency(coupon.minOrder)}
                </p>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                  Max Discount
                </label>
                <p className="text-xl font-bold text-green-900 dark:text-green-100">
                  {formatCurrency(coupon.maxDiscount)}
                </p>
              </div>

              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
                <label className="block text-sm font-medium text-purple-700 dark:text-purple-300 mb-1">
                  Usage Limit
                </label>
                <p className="text-xl font-bold text-purple-900 dark:text-purple-100">
                  {coupon.usageLimit.toLocaleString()}
                </p>
              </div>

              <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
                <label className="block text-sm font-medium text-orange-700 dark:text-orange-300 mb-1">
                  Times Used
                </label>
                <p className="text-xl font-bold text-orange-900 dark:text-orange-100">
                  {coupon.used.toLocaleString()}
                </p>
              </div>
            </div>

            {/* Usage Progress Bar */}
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                <span>Usage Progress</span>
                <span>{Math.round((coupon.used / coupon.usageLimit) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min((coupon.used / coupon.usageLimit) * 100, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-4 flex justify-end">
          <Button
            onClick={onClose}
            className="bg-[#FFD700] hover:bg-[#E6C200] text-black px-6 py-2"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CouponViewModal;
