// Centralized search configuration for global search functionality
// This file defines all searchable pages and actions across the application

export interface SearchAction {
  page: string;
  modal?: string;
  action?: string;
  params?: Record<string, any>;
}

export interface SearchItem {
  id: string;
  title: string;
  description: string;
  category: "Pages" | "Actions";
  keywords: string[];
  url?: string;
  action?: SearchAction;
  icon?: string;
}

// Centralized search mapping object
export const searchMap = {
  pages: {
    dashboard: "/",
    products: "/products",
    orders: "/orders",
    customers: "/customers",
    inventory: "/inventory",
    consultant: "/consultant",
    analytics: "/analytics",
    coupons: "/coupons",
    feedback: "/feedback",
    reviews: "/reviews",
    returns: "/returns",
    staff: "/staff",
    notifications: "/notifications",
    calendar: "/calendar",
    profile: "/profile",
  },
  actions: {
    "add product": { page: "/products", modal: "addProduct" },
    "create product": { page: "/products", modal: "addProduct" },
    "new product": { page: "/products", modal: "addProduct" },
    "create coupon": { page: "/coupons", modal: "createCoupon" },
    "add coupon": { page: "/coupons", modal: "createCoupon" },
    "new coupon": { page: "/coupons", modal: "createCoupon" },
    "create discount": { page: "/coupons", modal: "createDiscount" },
    "add discount": { page: "/coupons", modal: "createDiscount" },
    "add inventory": { page: "/inventory", modal: "addInventory" },
    "create inventory": { page: "/inventory", modal: "addInventory" },
    "add purchase": { page: "/inventory", modal: "addPurchase" },
    "view orders": { page: "/orders" },
    "send message": { page: "/customers", action: "sendMessage" },
    "view consultant": { page: "/consultant" },
    "export csv": { action: "exportCSV" },
    "export data": { action: "exportCSV" },
    "refresh data": { action: "refresh" },
    "refresh": { action: "refresh" },
    "add staff": { page: "/staff", modal: "addStaff" },
    "create staff": { page: "/staff", modal: "addStaff" },
    "view analytics": { page: "/analytics" },
    "view reports": { page: "/analytics" },
    "manage returns": { page: "/returns" },
    "view feedback": { page: "/feedback" },
    "manage reviews": { page: "/reviews" },
  },
};

// Comprehensive search data with all pages and actions
export const searchData: SearchItem[] = [
  // Pages
  {
    id: "dashboard",
    title: "Dashboard",
    description: "Main dashboard with overview and statistics",
    category: "Pages",
    url: "/",
    keywords: ["dashboard", "home", "overview", "main", "stats", "statistics"],
    icon: "📊",
  },
  {
    id: "products",
    title: "Products",
    description: "Manage products, inventory, and catalog",
    category: "Pages",
    url: "/products",
    keywords: ["products", "catalog", "items", "inventory", "stock"],
    icon: "📦",
  },
  {
    id: "orders",
    title: "Orders",
    description: "View and manage customer orders",
    category: "Pages",
    url: "/orders",
    keywords: ["orders", "purchases", "sales", "transactions"],
    icon: "🛒",
  },
  {
    id: "customers",
    title: "Customers",
    description: "Manage customer information and relationships",
    category: "Pages",
    url: "/customers",
    keywords: ["customers", "clients", "users", "contacts"],
    icon: "👥",
  },
  {
    id: "inventory",
    title: "Inventory",
    description: "Track stock levels and manage inventory",
    category: "Pages",
    url: "/inventory",
    keywords: ["inventory", "stock", "warehouse", "supplies"],
    icon: "📋",
  },
  {
    id: "consultant",
    title: "Consultant",
    description: "Patient management and consultation tracking",
    category: "Pages",
    url: "/consultant",
    keywords: ["consultant", "patients", "appointments", "medical"],
    icon: "👨‍⚕️",
  },
  {
    id: "analytics",
    title: "Analytics",
    description: "View reports and business analytics",
    category: "Pages",
    url: "/analytics",
    keywords: ["analytics", "reports", "charts", "data", "insights"],
    icon: "📈",
  },
  {
    id: "coupons",
    title: "Coupons & Referrals",
    description: "Manage discount coupons and referral programs",
    category: "Pages",
    url: "/coupons",
    keywords: ["coupons", "discounts", "referrals", "promotions"],
    icon: "🎫",
  },
  {
    id: "feedback",
    title: "Feedback",
    description: "View and manage customer feedback and complaints",
    category: "Pages",
    url: "/feedback",
    keywords: ["feedback", "complaints", "reviews", "support"],
    icon: "💬",
  },
  {
    id: "reviews",
    title: "Reviews",
    description: "Manage product and service reviews",
    category: "Pages",
    url: "/reviews",
    keywords: ["reviews", "ratings", "testimonials"],
    icon: "⭐",
  },
  {
    id: "returns",
    title: "Returns",
    description: "Handle product returns and refunds",
    category: "Pages",
    url: "/returns",
    keywords: ["returns", "refunds", "exchanges"],
    icon: "↩️",
  },
  {
    id: "staff",
    title: "Staff Management",
    description: "Manage staff members and permissions",
    category: "Pages",
    url: "/staff",
    keywords: ["staff", "employees", "team", "management"],
    icon: "👨‍💼",
  },

  // Actions
  {
    id: "add-product",
    title: "Add Product",
    description: "Create a new product in the catalog",
    category: "Actions",
    action: { page: "/products", modal: "addProduct" },
    keywords: ["add", "create", "new", "product"],
    icon: "➕",
  },
  {
    id: "create-coupon",
    title: "Create Coupon",
    description: "Create a new discount coupon",
    category: "Actions",
    action: { page: "/coupons", modal: "createCoupon" },
    keywords: ["create", "add", "new", "coupon", "discount"],
    icon: "🎫",
  },
  {
    id: "create-discount",
    title: "Create Discount",
    description: "Create a new discount offer",
    category: "Actions",
    action: { page: "/coupons", modal: "createDiscount" },
    keywords: ["create", "add", "new", "discount", "offer"],
    icon: "💰",
  },
  {
    id: "add-inventory",
    title: "Add Inventory",
    description: "Add new inventory items",
    category: "Actions",
    action: { page: "/inventory", modal: "addInventory" },
    keywords: ["add", "inventory", "stock", "items"],
    icon: "📦",
  },
  {
    id: "add-purchase",
    title: "Add Purchase",
    description: "Record a new purchase transaction",
    category: "Actions",
    action: { page: "/inventory", modal: "addPurchase" },
    keywords: ["add", "purchase", "buy", "transaction"],
    icon: "🛒",
  },
  {
    id: "export-csv",
    title: "Export CSV",
    description: "Export current page data to CSV",
    category: "Actions",
    action: { action: "exportCSV" },
    keywords: ["export", "csv", "download", "data"],
    icon: "📄",
  },
  {
    id: "refresh-data",
    title: "Refresh Data",
    description: "Refresh current page data",
    category: "Actions",
    action: { action: "refresh" },
    keywords: ["refresh", "reload", "update", "sync"],
    icon: "🔄",
  },
  {
    id: "send-message",
    title: "Send Message",
    description: "Send message to customers",
    category: "Actions",
    action: { page: "/customers", action: "sendMessage" },
    keywords: ["send", "message", "email", "contact"],
    icon: "✉️",
  },
  {
    id: "add-staff",
    title: "Add Staff",
    description: "Add new staff member",
    category: "Actions",
    action: { page: "/staff", modal: "addStaff" },
    keywords: ["add", "staff", "employee", "team"],
    icon: "👨‍💼",
  },
];

// Helper functions for search functionality
export const getSearchItemById = (id: string): SearchItem | undefined => {
  return searchData.find(item => item.id === id);
};

export const getPageUrl = (pageName: string): string | undefined => {
  return searchMap.pages[pageName.toLowerCase() as keyof typeof searchMap.pages];
};

export const getActionConfig = (actionName: string): SearchAction | undefined => {
  return searchMap.actions[actionName.toLowerCase() as keyof typeof searchMap.actions];
};

export const searchByKeywords = (query: string): SearchItem[] => {
  const searchTerm = query.toLowerCase().trim();
  if (!searchTerm) return [];

  return searchData.filter(item => {
    return (
      item.title.toLowerCase().includes(searchTerm) ||
      item.description.toLowerCase().includes(searchTerm) ||
      item.keywords.some(keyword => keyword.includes(searchTerm))
    );
  });
};
