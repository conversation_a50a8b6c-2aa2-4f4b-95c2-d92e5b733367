import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import AddCategoryModal from "./AddCategoryModal";
import { Plus } from "lucide-react";

interface InventoryItem {
  product: string;
  category: string;
  currentStock: number;
  stockLevel: string;
  value: string;
  lastRestocked: string;
  status: string;
}

interface AddPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: InventoryItem) => void;
}

const AddPurchaseModal = ({
  isOpen,
  onClose,
  onSubmit,
}: AddPurchaseModalProps) => {
  const [formData, setFormData] = useState<InventoryItem>({
    product: "",
    category: "",
    currentStock: 0,
    stockLevel: "",
    value: "",
    lastRestocked: "",
    status: "",
  });

  const [isAddCategoryModalOpen, setIsAddCategoryModalOpen] = useState(false);
  const [productCategories, setProductCategories] = useState([
    "Performance & Endurance Boosters",
    "Strength & Wellness Support",
  ]);

  const [availableProducts] = useState([
    { name: "Power Stride Juice", category: "Performance & Endurance Boosters" },
    { name: "Power Stride Capsules", category: "Performance & Endurance Boosters" },
    { name: "Power Stride Tablets", category: "Performance & Endurance Boosters" },
    { name: "L-large Sachets", category: "Strength & Wellness Support" },
  ]);

  const handleInputChange = (
    field: keyof InventoryItem,
    value: string | number
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleProductChange = (value: string) => {
    if (value === "add-category") {
      setIsAddCategoryModalOpen(true);
      return;
    }

    const selectedProduct = availableProducts.find((p) => p.name === value);
    setFormData((prev) => ({
      ...prev,
      product: value,
      category: selectedProduct?.category || "",
    }));
  };

  const handleAddCategory = (newCategory: string) => {
    setProductCategories((prev) => [...prev, newCategory]);
    setIsAddCategoryModalOpen(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.product ||
      !formData.category ||
      !formData.currentStock ||
      !formData.stockLevel ||
      !formData.value ||
      !formData.lastRestocked ||
      !formData.status
    ) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    onSubmit(formData);
    onClose();

    // Reset form
    setFormData({
      product: "",
      category: "",
      currentStock: 0,
      stockLevel: "",
      value: "",
      lastRestocked: "",
      status: "",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Inventory Item</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="product">Product *</Label>
              <Select
                value={formData.product}
                onValueChange={handleProductChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  {availableProducts.map((product) => (
                    <SelectItem key={product.name} value={product.name}>
                      {product.name}
                    </SelectItem>
                  ))}
                  <SelectItem
                    value="add-category"
                    className="text-green-600 font-medium"
                  >
                    <div className="flex items-center gap-2">
                      <Plus className="w-4 h-4" />
                      Add Category
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => handleInputChange("category", e.target.value)}
                placeholder="Product category"
                readOnly
                className="bg-gray-50 dark:bg-gray-700"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="currentStock">Current Stock *</Label>
              <Input
                id="currentStock"
                type="number"
                value={formData.currentStock || ""}
                onChange={(e) =>
                  handleInputChange("currentStock", Number(e.target.value))
                }
                placeholder="Enter current stock"
                min="0"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stockLevel">Stock Level *</Label>
              <Select
                value={formData.stockLevel}
                onValueChange={(value) =>
                  handleInputChange("stockLevel", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select stock level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="High">High</SelectItem>
                  <SelectItem value="Medium">Medium</SelectItem>
                  <SelectItem value="Low">Low</SelectItem>
                  <SelectItem value="Critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="value">Value *</Label>
              <Input
                id="value"
                type="text"
                value={formData.value}
                onChange={(e) => handleInputChange("value", e.target.value)}
                placeholder="e.g., ₹15,000"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="lastRestocked">Last Restocked *</Label>
              <Input
                id="lastRestocked"
                type="date"
                value={formData.lastRestocked}
                onChange={(e) =>
                  handleInputChange("lastRestocked", e.target.value)
                }
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status *</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="in stock">In Stock</SelectItem>
                  <SelectItem value="low stock">Low Stock</SelectItem>
                  <SelectItem value="out of stock">Out of Stock</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-green-600 hover:bg-green-700">
              Add to Inventory
            </Button>
          </div>
        </form>
      </DialogContent>

      <AddCategoryModal
        isOpen={isAddCategoryModalOpen}
        onClose={() => setIsAddCategoryModalOpen(false)}
        onAdd={handleAddCategory}
      />
    </Dialog>
  );
};

export default AddPurchaseModal;
