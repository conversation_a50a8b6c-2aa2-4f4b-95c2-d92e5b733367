import { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import {
  Search,
  ArrowRight,
  Zap,
  ExternalLink,
  Hash,
  FileText,
  Users,
  Package,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { searchData, SearchItem } from "@/config/searchConfig";
import { useSearch } from "@/contexts/SearchContext";

// Use SearchItem from config instead of local interface
type SearchResult = SearchItem;

interface IntelligentSearchProps {
  className?: string;
  placeholder?: string;
}

// Search data is now imported from centralized config

const IntelligentSearch: React.FC<IntelligentSearchProps> = ({
  className,
  placeholder = "Search across all data...",
}) => {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const navigate = useNavigate();
  const { executeSearch, isLoading } = useSearch();
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Search function with intelligent matching
  const performSearch = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    const query = searchQuery.toLowerCase().trim();
    const searchResults: (SearchResult & { score: number })[] = [];

    // Expand query to include partial matches and synonyms
    const expandedQueries = [
      query,
      ...query.split(" "),
      // Add common synonyms and variations
      ...(query.includes("add") ? ["create", "new"] : []),
      ...(query.includes("view") ? ["see", "show", "display"] : []),
      ...(query.includes("manage") ? ["edit", "update", "control"] : []),
    ];

    searchData.forEach((item) => {
      let score = 0;

      expandedQueries.forEach((searchTerm) => {
        // Exact title match (highest priority)
        if (item.title.toLowerCase() === searchTerm) {
          score += 100;
        }
        // Title starts with query
        else if (item.title.toLowerCase().startsWith(searchTerm)) {
          score += 80;
        }
        // Title contains query
        else if (item.title.toLowerCase().includes(searchTerm)) {
          score += 60;
        }

        // Keyword matches
        item.keywords.forEach((keyword) => {
          if (keyword === searchTerm) {
            score += 90;
          } else if (keyword.startsWith(searchTerm)) {
            score += 70;
          } else if (keyword.includes(searchTerm)) {
            score += 40;
          }
        });

        // Description contains query
        if (item.description.toLowerCase().includes(searchTerm)) {
          score += 30;
        }

        // Category matches
        if (item.category.toLowerCase().includes(searchTerm)) {
          score += 50;
        }
      });

      if (score > 0) {
        searchResults.push({ ...item, score });
      }
    });

    // Sort by score and limit results
    const sortedResults = searchResults
      .sort((a, b) => b.score - a.score)
      .slice(0, 8);

    setResults(sortedResults);
  }, []);

  // Handle input change with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query);
    }, 150);

    return () => clearTimeout(timeoutId);
  }, [query, performSearch]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) => (prev < results.length - 1 ? prev + 1 : 0));
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : results.length - 1));
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          handleResultClick(results[selectedIndex]);
        }
        break;
      case "Escape":
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle result selection
  const handleResultClick = async (result: SearchResult) => {
    try {
      await executeSearch(result);
      setQuery("");
      setResults([]);
      setIsOpen(false);
      setSelectedIndex(-1);
      inputRef.current?.blur();
    } catch (error) {
      console.error("Search execution failed:", error);
    }
  };

  // Handle input focus/blur
  const handleFocus = () => {
    if (query.trim() && results.length > 0) {
      setIsOpen(true);
    }
  };

  const handleBlur = () => {
    // Delay closing to allow for result clicks
    setTimeout(() => {
      setIsOpen(false);
      setSelectedIndex(-1);
    }, 200);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchRef.current &&
        !searchRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Get icon for category
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Pages":
        return <FileText className="w-4 h-4" />;
      case "Actions":
        return <Zap className="w-4 h-4" />;
      default:
        return <Hash className="w-4 h-4" />;
    }
  };

  return (
    <div ref={searchRef} className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4 transition-colors duration-500 ease-in-out" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className="pl-10 pr-4 py-2 w-full bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 focus:ring-2 focus:ring-[#FFD700] focus:border-transparent theme-transition text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 shadow-sm"
          disabled={isLoading}
        />
        {isLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#FFD700]"></div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && results.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Group results by category */}
          {["Pages", "Actions"].map((category) => {
            const categoryResults = results.filter((r) => r.category === category);
            if (categoryResults.length === 0) return null;

            return (
              <div key={category}>
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  {category}
                </div>
                {categoryResults.map((result, index) => {
                  const globalIndex = results.indexOf(result);
                  return (
                    <div
                      key={result.id}
                      className={cn(
                        "px-3 py-3 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-b-0 transition-colors duration-150",
                        globalIndex === selectedIndex
                          ? "bg-[#FFD700]/10 dark:bg-[#FFD700]/20"
                          : "hover:bg-gray-50 dark:hover:bg-gray-700"
                      )}
                      onClick={() => handleResultClick(result)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex-shrink-0 text-gray-400 dark:text-gray-500">
                          {result.icon ? (
                            <span className="text-lg">{result.icon}</span>
                          ) : (
                            getCategoryIcon(result.category)
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {result.title}
                            </p>
                            {result.category === "Actions" && (
                              <ExternalLink className="w-3 h-3 text-gray-400" />
                            )}
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {result.description}
                          </p>
                        </div>
                        <ArrowRight className="w-4 h-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
                      </div>
                    </div>
                  );
                })}
              </div>
            );
          })}
        </div>
      )}

      {/* No Results */}
      {isOpen && query.trim() && results.length === 0 && !isLoading && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 p-4 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            No matching results. Please try a different keyword.
          </p>
        </div>
      )}
    </div>
  );
};

export default IntelligentSearch;
