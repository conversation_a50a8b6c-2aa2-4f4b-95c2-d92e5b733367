import React, { useEffect } from "react";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface TermsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const TermsModal: React.FC<TermsModalProps> = ({ isOpen, onClose }) => {
  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] overflow-hidden bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 mx-auto my-auto">
        <DialogHeader className="border-b border-gray-200 dark:border-gray-700 pb-4">
          <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
            Terms & Conditions
          </DialogTitle>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[70vh] p-6 space-y-6 text-gray-700 dark:text-gray-300">
          {/* TODO: Replace with actual Terms & Conditions content from legal team */}

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              1. Acceptance of Terms
            </h3>
            <p className="leading-relaxed">
              Welcome to Dr. Kumar Laboratories. By accessing and using our
              services, you acknowledge that you have read, understood, and
              agree to be bound by these Terms & Conditions. If you do not agree
              to these terms, please do not use our services.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              2. Use of Services
            </h3>
            <p className="leading-relaxed mb-3">
              Our platform provides laboratory management and healthcare
              services. You agree to use our services only for lawful purposes
              and in accordance with these terms.
            </p>
            <ul className="list-disc list-inside space-y-2 ml-4">
              <li>You must provide accurate and complete information</li>
              <li>
                You are responsible for maintaining the confidentiality of your
                account
              </li>
              <li>
                You must not use our services for any illegal or unauthorized
                purpose
              </li>
              <li>You must not interfere with or disrupt our services</li>
            </ul>
          </section>

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              3. Account Registration
            </h3>
            <p className="leading-relaxed">
              To access certain features of our services, you may be required to
              create an account. You are responsible for maintaining the
              security of your account and password. Dr. Kumar Laboratories
              cannot and will not be liable for any loss or damage arising from
              your failure to comply with this security obligation.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              4. Medical Information
            </h3>
            <p className="leading-relaxed">
              Any medical information provided through our platform is for
              informational purposes only and should not be considered as
              professional medical advice. Always consult with qualified
              healthcare professionals for medical concerns.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              5. Privacy and Data Protection
            </h3>
            <p className="leading-relaxed">
              Your privacy is important to us. Please review our Privacy Policy
              to understand how we collect, use, and protect your personal
              information. By using our services, you consent to the collection
              and use of your information as described in our Privacy Policy.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              6. Limitation of Liability
            </h3>
            <p className="leading-relaxed">
              Dr. Kumar Laboratories shall not be liable for any indirect,
              incidental, special, consequential, or punitive damages, including
              without limitation, loss of profits, data, use, goodwill, or other
              intangible losses, resulting from your use of our services.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              7. Modifications to Terms
            </h3>
            <p className="leading-relaxed">
              We reserve the right to modify these Terms & Conditions at any
              time. We will notify users of any material changes by posting the
              new terms on our platform. Your continued use of our services
              after such modifications constitutes your acceptance of the
              updated terms.
            </p>
          </section>

          <section>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              8. Contact Information
            </h3>
            <p className="leading-relaxed">
              If you have any questions about these Terms & Conditions, please
              contact <NAME_EMAIL> or through our customer
              support channels.
            </p>
          </section>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mt-6">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Last Updated:</strong> January 2024
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              These terms are effective as of the date of your acceptance.
            </p>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-4 flex justify-end">
          <Button
            onClick={onClose}
            className="bg-[#FFD700] hover:bg-[#E6C200] text-black px-6 py-2"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TermsModal;
